/**
 * Simple Fallback Polling Hook
 *
 * Provides basic polling when real-time connection is down.
 * Minimal, focused implementation without over-engineering.
 */

import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

interface FallbackPollingOptions {
  queryKey: string[];
  enabled: boolean;
  interval?: number; // Default 30 seconds
}

/**
 * Simple polling hook that activates when real-time is unreliable
 */
export function useFallbackPolling({
  queryKey,
  enabled,
  interval = 30000,
}: FallbackPollingOptions) {
  const queryClient = useQueryClient();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!enabled) {
      // Clear any existing interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // Start polling
    intervalRef.current = setInterval(() => {
      queryClient.invalidateQueries({
        queryKey,
        exact: false,
        refetchType: 'active',
      });
    }, interval);

    // Cleanup on unmount or when disabled
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enabled, interval, queryKey, queryClient]);
}

/**
 * Simple connection health tracking
 * Returns true if real-time seems to be working
 */
export function useSimpleConnectionHealth(_tenantId: string): boolean {
  // Simple approach: assume connection is healthy by default
  // This can be enhanced later if needed, but keeps it minimal for now
  return true; // TODO: Add simple connection detection if needed
}

/**
 * Hook that combines real-time query with simple fallback polling
 */
export function useRealtimeWithFallback(
  queryKey: string[],
  options: {
    enabled?: boolean;
    tenantId: string;
  }
) {
  const { enabled = true, tenantId } = options;
  const isConnectionHealthy = useSimpleConnectionHealth(tenantId);

  // Enable fallback polling when connection seems unhealthy
  useFallbackPolling({
    queryKey,
    enabled: enabled && !isConnectionHealthy,
    interval: 30000, // 30 seconds
  });

  return {
    isConnectionHealthy,
    shouldPoll: !isConnectionHealthy,
  };
}

/**
 * Simple silent validation function
 * Call this when users interact with tickets to ensure fresh data
 */
export function silentValidate(
  queryClient: ReturnType<typeof useQueryClient>,
  queryKey: string[]
) {
  // Simple approach: just invalidate the query silently
  queryClient.invalidateQueries({
    queryKey,
    exact: false,
    refetchType: 'active',
  });
}
