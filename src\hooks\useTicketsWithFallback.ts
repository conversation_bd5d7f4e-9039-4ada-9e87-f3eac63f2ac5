/**
 * Simple Tickets Hook with Fallback
 *
 * Minimal example showing how to use the simplified fallback system.
 * This is a clean, focused implementation without over-engineering.
 */

import { useQueryClient } from '@tanstack/react-query';
import { useRealtimeTickets } from './useRealtimeQuery';
import { silentValidate } from './useFallbackPolling';
import type {
  RoleBasedFilterContext,
  TicketFilterOptions,
} from './useRealtimeQuery';

interface UseTicketsWithFallbackOptions extends TicketFilterOptions {
  enabled?: boolean;
}

/**
 * Enhanced tickets hook with simple fallback capabilities
 */
export function useTicketsWithFallback(
  context: RoleBasedFilterContext,
  options: UseTicketsWithFallbackOptions = {}
) {
  const queryClient = useQueryClient();

  // Use the existing enhanced tickets hook
  const ticketsQuery = useRealtimeTickets(context, options);

  // Simple function to validate data when users interact with tickets
  const validateOnInteraction = (ticketId?: string) => {
    const baseKey = ['tickets', context.tenantId];

    // Validate the main tickets list
    silentValidate(queryClient, baseKey);

    // If a specific ticket is provided, validate its details too
    if (ticketId) {
      silentValidate(queryClient, [...baseKey, ticketId]);
      silentValidate(queryClient, [...baseKey, ticketId, 'messages']);
    }
  };

  return {
    ...ticketsQuery,

    // Simple function to call when users click on tickets
    onTicketClick: (ticketId: string) => {
      validateOnInteraction(ticketId);
    },

    // Simple function to call when users view the tickets list
    onListView: () => {
      validateOnInteraction();
    },

    // Manual validation function
    validate: validateOnInteraction,
  };
}

/**
 * Simple hook for ticket details with fallback
 */
export function useTicketDetailWithFallback(
  tenantId: string,
  ticketId: string,
  enabled: boolean = true
) {
  const queryClient = useQueryClient();

  // Simple validation for ticket details (only if enabled)
  const validateTicketDetail = () => {
    if (!enabled) return;
    const detailKey = ['tickets', tenantId, ticketId];
    const messagesKey = ['tickets', tenantId, ticketId, 'messages'];

    silentValidate(queryClient, detailKey);
    silentValidate(queryClient, messagesKey);
  };

  return {
    validate: validateTicketDetail,
    onFocus: validateTicketDetail,
    onMount: validateTicketDetail,
  };
}

/**
 * Simple usage example:
 *
 * ```typescript
 * function TicketsList({ tenantId }: { tenantId: string }) {
 *   const tickets = useTicketsWithFallback(
 *     { tenantId, userId: 'current-user' },
 *     { enabled: true }
 *   );
 *
 *   const handleTicketClick = (ticketId: string) => {
 *     // This will silently validate the ticket data
 *     tickets.onTicketClick(ticketId);
 *
 *     // Your navigation logic
 *     navigate(`/tickets/${ticketId}`);
 *   };
 *
 *   // Validate when component mounts
 *   useEffect(() => {
 *     tickets.onListView();
 *   }, []);
 *
 *   return (
 *     <div>
 *       {tickets.data?.map(ticket => (
 *         <div key={ticket.id} onClick={() => handleTicketClick(ticket.id)}>
 *           {ticket.title}
 *         </div>
 *       ))}
 *     </div>
 *   );
 * }
 * ```
 */
