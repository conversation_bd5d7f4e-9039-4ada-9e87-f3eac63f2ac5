'use client';

import { createContext, useContext, useEffect, useMemo, useState } from 'react';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { useAuth } from '@clerk/nextjs';
import { Database } from '@/lib/database.types';

interface SupabaseContextType {
  supabase: SupabaseClient<Database> | null;
  isInitialized: boolean;
}

const SupabaseContext = createContext<SupabaseContextType | undefined>(
  undefined
);

// --- Centralized Supabase Client Instance ---
// This ensures that there is only one instance of the Supabase client throughout the app.
let supabaseInstance: SupabaseClient<Database> | null = null;

export const SupabaseProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const { getToken, isSignedIn } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);

  const supabase = useMemo(() => {
    if (supabaseInstance) {
      return supabaseInstance;
    }

    if (
      !process.env.NEXT_PUBLIC_SUPABASE_URL ||
      !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    ) {
      throw new Error(
        'Supabase URL or Anon Key is not defined in environment variables.'
      );
    }

    supabaseInstance = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        global: {
          fetch: async (url, options = {}) => {
            // CRITICAL FIX: Enhanced token acquisition with retry logic
            let token = await getToken({ template: 'supabase' });

            // Retry logic for login transitions
            if (!token) {
              // Wait briefly and try again
              await new Promise((resolve) => setTimeout(resolve, 100));
              token = await getToken({ template: 'supabase' });
            }

            const headers = new Headers(options.headers);
            if (token) {
              headers.set('Authorization', `Bearer ${token}`);
            }
            return fetch(url, { ...options, headers });
          },
        },
        realtime: {
          params: {
            eventsPerSecond: 20, // Optimized for high-performance real-time updates
          },
        },
      }
    );

    return supabaseInstance;
  }, [getToken]);

  useEffect(() => {
    if (supabase && isSignedIn) {
      setIsInitialized(true);
    } else {
      setIsInitialized(false);
    }
  }, [supabase, isSignedIn]);

  return (
    <SupabaseContext.Provider value={{ supabase, isInitialized }}>
      {children}
    </SupabaseContext.Provider>
  );
};

export const useSupabase = () => {
  const context = useContext(SupabaseContext);
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return context;
};
